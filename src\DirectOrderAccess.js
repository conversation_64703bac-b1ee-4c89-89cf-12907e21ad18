import React, { useState } from 'react';
import { authenticatedFetch } from './authService';

/**
 * Component that demonstrates direct access to protected resource
 * This simulates the scenario where a user directly calls http://localhost:8091/orders
 */
export default function DirectOrderAccess() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [accessAttempted, setAccessAttempted] = useState(false);

  const handleDirectAccess = async () => {
    setLoading(true);
    setError(null);
    setAccessAttempted(true);
    
    try {
      console.log('🔍 Attempting direct access to http://localhost:8091/orders');
      console.log('📋 This will check authentication and redirect to login if needed...');
      
      const response = await authenticatedFetch('/orders');
      
      if (response) {
        const ordersData = await response.json();
        setOrders(ordersData);
        console.log('✅ Successfully retrieved orders:', ordersData);
      }
    } catch (err) {
      setError(err.message);
      console.error('❌ Error accessing orders:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🔐 Protected Resource Access Demo</h2>
      
      <div style={{ 
        backgroundColor: '#f0f8ff', 
        padding: '15px', 
        borderRadius: '5px', 
        marginBottom: '20px',
        border: '1px solid #0066cc'
      }}>
        <h3>Scenario:</h3>
        <p>
          This demonstrates what happens when a user tries to access 
          <code style={{ backgroundColor: '#e6e6e6', padding: '2px 4px', borderRadius: '3px' }}>
            http://localhost:8091/orders
          </code> 
          on the resource server.
        </p>
        <ul>
          <li>✅ If authenticated: Returns order details immediately</li>
          <li>🔄 If not authenticated: Redirects to OAuth login</li>
          <li>⚠️ If token expired: Attempts refresh or redirects to login</li>
        </ul>
      </div>

      <button 
        onClick={handleDirectAccess}
        disabled={loading}
        style={{
          backgroundColor: '#0066cc',
          color: 'white',
          padding: '12px 24px',
          border: 'none',
          borderRadius: '5px',
          fontSize: '16px',
          cursor: loading ? 'not-allowed' : 'pointer',
          opacity: loading ? 0.6 : 1
        }}
      >
        {loading ? '🔄 Accessing...' : '🚀 Access Orders (http://localhost:8091/orders)'}
      </button>

      {loading && (
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#fff3cd', borderRadius: '5px' }}>
          <p>🔍 Checking authentication status...</p>
          <p>📡 Attempting to access protected resource...</p>
        </div>
      )}

      {error && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#f8d7da', 
          borderRadius: '5px',
          border: '1px solid #dc3545',
          color: '#721c24'
        }}>
          <h4>❌ Access Failed</h4>
          <p><strong>Error:</strong> {error}</p>
          <p>This usually means authentication is required or the resource server is not available.</p>
        </div>
      )}

      {orders.length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <h3>✅ Orders Retrieved Successfully!</h3>
          <p style={{ color: '#28a745' }}>
            🎉 Authentication successful! Here are the orders from the resource server:
          </p>
          
          <div style={{ 
            backgroundColor: '#d4edda', 
            padding: '15px', 
            borderRadius: '5px',
            border: '1px solid #28a745'
          }}>
            {orders.map((order, index) => (
              <div key={order.id || index} style={{
                backgroundColor: 'white',
                margin: '10px 0',
                padding: '15px',
                borderRadius: '5px',
                border: '1px solid #c3e6cb'
              }}>
                <h4>📦 Order #{index + 1}</h4>
                <p><strong>Order ID:</strong> {order.id}</p>
                <p><strong>Product ID:</strong> {order.productId}</p>
                <p><strong>User ID:</strong> {order.userId}</p>
                <p><strong>Quantity:</strong> {order.quantity}</p>
                <p><strong>Status:</strong> <span style={{
                  backgroundColor: order.status === 'NEW' ? '#28a745' : '#6c757d',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '3px',
                  fontSize: '12px'
                }}>{order.status}</span></p>
              </div>
            ))}
          </div>
        </div>
      )}

      {accessAttempted && !loading && !error && orders.length === 0 && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#fff3cd', 
          borderRadius: '5px',
          border: '1px solid #ffc107'
        }}>
          <p>⚠️ No orders returned. This might mean:</p>
          <ul>
            <li>The resource server returned an empty list</li>
            <li>You were redirected to login (check browser navigation)</li>
            <li>The request is still being processed</li>
          </ul>
        </div>
      )}

      <div style={{ 
        marginTop: '30px', 
        padding: '15px', 
        backgroundColor: '#e9ecef', 
        borderRadius: '5px',
        fontSize: '14px'
      }}>
        <h4>🔧 Technical Details:</h4>
        <ul>
          <li><strong>Authorization Server:</strong> http://127.0.0.1:8000</li>
          <li><strong>Resource Server:</strong> http://localhost:8091</li>
          <li><strong>Client ID:</strong> client1</li>
          <li><strong>Required Scopes:</strong> openid, read, mywrite</li>
        </ul>
      </div>
    </div>
  );
}
