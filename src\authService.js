import { UserManager } from 'oidc-client-ts';

/*
const oidcConfig = {
  authority: 'http://localhost:9000',
  client_id: 'client1',
  redirect_uri: 'http://localhost:3000/callback', // 👈 switch back to localhost
  response_type: 'code',
  scope: 'openid profile' // 👈 include all registered scopes
};
*/

/*
const oidcConfig = {
  authority: 'http://127.0.0.1:8000',
  client_id: 'client1', // use same as registered client
  redirect_uri: 'http://127.0.0.1:3000/callback',
  response_type: 'code',
  scope: 'openid read mywrite',
  // Override metadata to handle issuer mismatch
  metadata: {
    issuer: 'http://127.0.0.1:8000',
    authorization_endpoint: 'http://127.0.0.1:8000/oauth2/authorize',
    token_endpoint: 'http://127.0.0.1:8000/oauth2/token',
    userinfo_endpoint: 'http://127.0.0.1:8000/userinfo',
    jwks_uri: 'http://127.0.0.1:8000/oauth2/jwks'
  }
};
*/
const oidcConfig = {
  authority: 'http://auth-server:8000',
  client_id: 'client1', // use same as registered client
  redirect_uri: 'http://127.0.0.1:3000/callback/authorized',
  response_type: 'code',
  scope: 'openid read mywrite',
  // Override metadata to handle issuer mismatch
  metadata: {
    issuer: 'http://auth-server:8000',
    authorization_endpoint: 'http://auth-server:8000/oauth2/authorize',
    token_endpoint: 'http://auth-server:8000/oauth2/token',
    userinfo_endpoint: 'http://auth-server:8000/userinfo',
    jwks_uri: 'http://auth-server:8000/oauth2/jwks'
  }
};


const userManager = new UserManager(oidcConfig);
export default userManager;