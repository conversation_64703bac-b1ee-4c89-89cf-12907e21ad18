import { UserManager } from 'oidc-client-ts';

/*
const oidcConfig = {
  authority: 'http://localhost:9000',
  client_id: 'client1',
  redirect_uri: 'http://localhost:3000/callback', // 👈 switch back to localhost
  response_type: 'code',
  scope: 'openid profile' // 👈 include all registered scopes
};
*/

const oidcConfig = {
  authority: 'http://auth-server:8000',
  client_id: 'client1', // use same as registered client
  redirect_uri: 'http://127.0.0.1:3000/callback',
  response_type: 'code',
  scope: 'openid read mywrite'
};
const userManager = new UserManager(oidcConfig);
export default userManager;