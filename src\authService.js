import { UserManager } from 'oidc-client-ts';
const auth_server = "auth-server";
// OAuth Configuration for Authorization Server
const oidcConfig = {
  authority: 'http://auth-server:8000',
  client_id: 'client1',
  redirect_uri: 'http://127.0.0.1:3000/callback/authorized',
  response_type: 'code',
  scope: 'openid read mywrite',
  // Override metadata to handle issuer mismatch
  metadata: {
    issuer: 'http://auth-server:8000',
    authorization_endpoint: 'http://auth-server:8000/oauth2/authorize',
    token_endpoint: 'http://auth-server:8000/oauth2/token',
    userinfo_endpoint: 'http://auth-server:8000/userinfo',
    jwks_uri: 'http://auth-server:8000/oauth2/jwks'
  }
};

const userManager = new UserManager(oidcConfig);

// Resource Server Configuration
const RESOURCE_SERVER_URL = 'http://localhost:8091';

/**
 * Makes an authenticated request to the resource server
 * If user is not authenticated, triggers OAuth login flow
 * @param {string} endpoint - The API endpoint (e.g., '/orders')
 * @param {object} options - Fetch options (method, headers, body, etc.)
 * @returns {Promise} - Response from the resource server
 */
export const authenticatedFetch = async (endpoint, options = {}) => {
  try {
    // Check if user is authenticated
    let user = await userManager.getUser();

    if (!user || !user.access_token) {
      console.log('No valid access token found, initiating login...');
      // Store the intended destination for after login
      sessionStorage.setItem('postLoginRedirect', endpoint);
      // Trigger OAuth login flow
      await userManager.signinRedirect();
      return; // This will redirect, so we return here
    }

    // Check if token is expired
    if (user.expired) {
      console.log('Access token expired, attempting refresh...');
      try {
        user = await userManager.signinSilent();
      } catch (error) {
        console.log('Silent refresh failed, initiating login...');
        sessionStorage.setItem('postLoginRedirect', endpoint);
        await userManager.signinRedirect();
        return;
      }
    }

    // Make authenticated request to resource server
    const url = `${RESOURCE_SERVER_URL}${endpoint}`;
    const authenticatedOptions = {
      ...options,
      headers: {
        'Authorization': `Bearer ${user.access_token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    console.log(`Making authenticated request to: ${url}`);
    const response = await fetch(url, authenticatedOptions);

    if (response.status === 401) {
      console.log('Received 401, token may be invalid, initiating login...');
      sessionStorage.setItem('postLoginRedirect', endpoint);
      await userManager.signinRedirect();
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error('Error in authenticatedFetch:', error);
    throw error;
  }
};

/**
 * Fetches orders from the resource server with authentication
 * @returns {Promise<Array>} - Array of order objects
 */
export const fetchOrders = async () => {
  try {
    const response = await authenticatedFetch('/orders');
    if (response) {
      return await response.json();
    }
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};

/**
 * Handles post-login redirect to intended resource
 */
export const handlePostLoginRedirect = async () => {
  const intendedEndpoint = sessionStorage.getItem('postLoginRedirect');
  if (intendedEndpoint) {
    sessionStorage.removeItem('postLoginRedirect');
    // Redirect to the orders page or handle the intended action
    window.location.href = '/orders';
  }
};

export default userManager;