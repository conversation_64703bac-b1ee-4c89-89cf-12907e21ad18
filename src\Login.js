import React from 'react';
import { Link } from 'react-router-dom';
import userManager from './authService';

export default function Login() {
  const login = () => userManager.signinRedirect();

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>🔐 OAuth 2.0 Resource Server Demo</h1>

      <div style={{
        backgroundColor: '#f0f8ff',
        padding: '20px',
        borderRadius: '10px',
        marginBottom: '30px',
        border: '1px solid #0066cc'
      }}>
        <h2>Welcome to the OAuth Demo!</h2>
        <p>
          This demo shows how OAuth 2.0 authentication works when accessing protected resources.
        </p>

        <h3>🎯 Demo Scenario:</h3>
        <p>
          When you try to access <code>http://localhost:8091/orders</code> on the resource server:
        </p>
        <ol>
          <li>🔍 System checks if you're authenticated</li>
          <li>🔄 If not authenticated, redirects to OAuth login</li>
          <li>✅ After successful login, you get access to order details</li>
          <li>🔒 All subsequent requests use your access token</li>
        </ol>
      </div>

      <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap' }}>
        <div style={{ flex: 1, minWidth: '300px' }}>
          <h3>🚀 Try Direct Access</h3>
          <p>
            Experience the authentication flow by trying to access protected resources directly:
          </p>
          <Link
            to="/direct-access"
            style={{
              display: 'inline-block',
              backgroundColor: '#28a745',
              color: 'white',
              padding: '12px 24px',
              textDecoration: 'none',
              borderRadius: '5px',
              fontSize: '16px'
            }}
          >
            🎯 Demo Direct Access
          </Link>
        </div>

        <div style={{ flex: 1, minWidth: '300px' }}>
          <h3>🔑 Manual Login</h3>
          <p>
            Or login manually first, then access protected resources:
          </p>
          <button
            onClick={login}
            style={{
              backgroundColor: '#0066cc',
              color: 'white',
              padding: '12px 24px',
              border: 'none',
              borderRadius: '5px',
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            🔐 Login with OAuth
          </button>
        </div>
      </div>

      <div style={{
        marginTop: '30px',
        padding: '15px',
        backgroundColor: '#e9ecef',
        borderRadius: '5px'
      }}>
        <h4>🔧 System Configuration:</h4>
        <ul>
          <li><strong>Authorization Server:</strong> http://auth-server:8000</li>
          <li><strong>Resource Server:</strong> http://localhost:8091</li>
          <li><strong>React SPA:</strong> http://127.0.0.1:3000</li>
          <li><strong>Client ID:</strong> client1</li>
          <li><strong>Scopes:</strong> openid, read, mywrite</li>
        </ul>
      </div>
    </div>
  );
}