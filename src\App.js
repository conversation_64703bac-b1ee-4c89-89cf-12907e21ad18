import React from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import Login from './Login';
import Callback from './Callback';
import Protected from './Protected';
import Orders from './Orders';
import DirectOrderAccess from './DirectOrderAccess';

function Navigation() {
  const location = useLocation();

  // Don't show navigation on callback page
  if (location.pathname === '/callback') {
    return null;
  }

  return (
    <nav style={{
      backgroundColor: '#f8f9fa',
      padding: '10px 20px',
      borderBottom: '1px solid #dee2e6',
      marginBottom: '20px'
    }}>
      <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
        <h3 style={{ margin: 0, color: '#0066cc' }}>🔐 OAuth Demo</h3>
        <div style={{ display: 'flex', gap: '15px' }}>
          <Link to="/" style={{ textDecoration: 'none', color: '#0066cc' }}>
            🏠 Home
          </Link>
          <Link to="/direct-access" style={{ textDecoration: 'none', color: '#0066cc' }}>
            🚀 Direct Access Demo
          </Link>
          <Link to="/orders" style={{ textDecoration: 'none', color: '#0066cc' }}>
            📦 Orders
          </Link>
          <Link to="/protected" style={{ textDecoration: 'none', color: '#0066cc' }}>
            🔒 Protected
          </Link>
        </div>
      </div>
    </nav>
  );
}

export default function App() {
  return (
    <div>
      <Navigation />
      <Routes>
        <Route path="/" element={<Login />} />
        <Route path="/callback" element={<Callback />} />
        <Route path="/callback/authorized" element={<Callback />} />
        <Route path="/protected" element={<Protected />} />
        <Route path="/orders" element={<Orders />} />
        <Route path="/direct-access" element={<DirectOrderAccess />} />
      </Routes>
    </div>
  );
}