import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Login from './Login';
import Callback from './Callback';
import Protected from './Protected';
import Orders from './Orders';

export default function App() {
  return (
    <Routes>
      <Route path="/" element={<Login />} />
      <Route path="/callback" element={<Callback />} />
      <Route path="/protected" element={<Protected />} />
      <Route path="/orders" element={<Orders />} />
    </Routes>
  );
}