import React, { useEffect, useState } from 'react';
import userManager from './authService';

export default function Orders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        // Get the current user with access token
        const user = await userManager.getUser();
        
        if (!user || !user.access_token) {
          throw new Error('No access token available');
        }

        console.log('Access Token:', user.access_token);

        // Make authenticated request to resource server
        const response = await fetch('http://127.0.0.1:8091/orders', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${user.access_token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const ordersData = await response.json();
        setOrders(ordersData);
      } catch (err) {
        setError(err.message);
        console.error('Error fetching orders:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  if (loading) return <div>Loading orders...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Orders</h2>
      {orders.length === 0 ? (
        <p>No orders found</p>
      ) : (
        <ul>
          {orders.map((order, index) => (
            <li key={index}>{JSON.stringify(order)}</li>
          ))}
        </ul>
      )}
    </div>
  );
}