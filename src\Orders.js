import React, { useEffect, useState } from 'react';
import { fetchOrders } from './authService';

export default function Orders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadOrders = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Attempting to fetch orders from resource server...');
        const ordersData = await fetchOrders();

        if (ordersData) {
          setOrders(ordersData);
          console.log('Orders loaded successfully:', ordersData);
        }
      } catch (err) {
        setError(err.message);
        console.error('Error loading orders:', err);
      } finally {
        setLoading(false);
      }
    };

    loadOrders();
  }, []);

  if (loading) return (
    <div>
      <h2>Orders</h2>
      <p>Loading orders... (Checking authentication)</p>
    </div>
  );

  if (error) return (
    <div>
      <h2>Orders</h2>
      <div style={{color: 'red'}}>Error: {error}</div>
      <p>Please try refreshing the page or logging in again.</p>
    </div>
  );

  return (
    <div>
      <h2>Orders</h2>
      <p>Successfully authenticated! Here are your orders:</p>
      {orders.length === 0 ? (
        <p>No orders found</p>
      ) : (
        <div>
          <p>Found {orders.length} order(s):</p>
          <ul style={{listStyle: 'none', padding: 0}}>
            {orders.map((order, index) => (
              <li key={order.id || index} style={{
                border: '1px solid #ccc',
                margin: '10px 0',
                padding: '10px',
                borderRadius: '5px',
                backgroundColor: '#f9f9f9'
              }}>
                <strong>Order ID:</strong> {order.id}<br/>
                <strong>Product ID:</strong> {order.productId}<br/>
                <strong>User ID:</strong> {order.userId}<br/>
                <strong>Quantity:</strong> {order.quantity}<br/>
                <strong>Status:</strong> {order.status}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}