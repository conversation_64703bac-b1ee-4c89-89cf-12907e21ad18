import React, { useEffect } from 'react';
import userManager from './authService';

export default function Callback() {
  useEffect(() => {
    userManager.signinRedirectCallback().then(user => {
      console.log('Logged in user:', user);
      window.location.href = '/protected'; // or home page
    }).catch(error => {
      console.error('Login error:', error);
    });
  }, []);

  return <p>Signing in...</p>;
}
