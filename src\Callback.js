import React, { useEffect, useState } from 'react';
import userManager from './authService';

export default function Callback() {
  const [error, setError] = useState(null);

  useEffect(() => {
    userManager.signinRedirectCallback().then(user => {
      console.log('Logged in user:', user);
      window.location.href = '/protected';
    }).catch(error => {
      console.error('Login error:', error);
      setError(error.message);
    });
  }, []);

  if (error) {
    return <div>Error: {error}</div>;
  }

  return <p>Signing in...</p>;
}

