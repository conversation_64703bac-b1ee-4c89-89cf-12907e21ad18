import React, { useEffect, useState } from 'react';
import userManager from './authService';

export default function Callback() {
  const [error, setError] = useState(null);

  useEffect(() => {
    userManager.signinRedirectCallback().then(user => {
      console.log('Logged in user:', user);

      // Check if there was an intended redirect after login
      const intendedEndpoint = sessionStorage.getItem('postLoginRedirect');
      if (intendedEndpoint) {
        console.log('Redirecting to intended resource:', intendedEndpoint);
        sessionStorage.removeItem('postLoginRedirect');
        window.location.href = '/orders';
      } else {
        // Default redirect to protected page
        window.location.href = '/protected';
      }
    }).catch(error => {
      console.error('Login error:', error);
      setError(error.message);
    });
  }, []);

  if (error) {
    return (
      <div>
        <h2>Authentication Error</h2>
        <div style={{color: 'red'}}>Error: {error}</div>
        <p>Please try logging in again.</p>
        <button onClick={() => window.location.href = '/'}>
          Back to Login
        </button>
      </div>
    );
  }

  return (
    <div>
      <h2>Authenticating...</h2>
      <p>Processing your login and redirecting...</p>
    </div>
  );
}

